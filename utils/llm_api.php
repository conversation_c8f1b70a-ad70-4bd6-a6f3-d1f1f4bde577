<?php
declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\Yaml\Yaml;

function get_provider_config(string $provider): array
{
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
    $dotenv->load();

    switch ($provider) {
        case 'OLLAMA':
            return [
                'api_key' => $_ENV['OLLAMA_API_KEY'] ?? 'ollama',
                'base_uri' => 'http://localhost:11434',
            ];
        case 'LM_STUDIO':
            return [
                'api_key' => $_ENV['LMSTUDIO_API_KEY'] ?? 'lmstudio',
                'base_uri' => 'http://localhost:1234',
            ];
        case 'MISTRAL':
            return [
                'api_key' => $_ENV['MISTRAL_API_KEY'] ?? '',
                'base_uri' => 'https://api.mistral.ai',
            ];
        case 'DEEPSEEK':
            return [
                'api_key' => $_ENV['DEEPSEEK_API_KEY'] ?? '',
                'base_uri' => 'https://api.deepseek.com',
            ];
        case 'OPENAI_COMPATIBLE':
        default:
            return [
                'api_key' => $_ENV['OPENAI_COMPATIBLE_API_KEY'] ?? '',
                'base_uri' => $_ENV['OPENAI_COMPATIBLE_BASE_URL'] ?? 'https://api.openai.com/v1',
            ];
    }
}

function call_llm_stream(string $provider, string $model, array $messages): Closure
{
    return function () use ($provider, $model, $messages) {
        $config = get_provider_config($provider);
        $client = new Client(['base_uri' => $config['base_uri']]);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $config['api_key'],
        ];

        $body = json_encode([
            'model' => $model,
            'messages' => $messages,
            'stream' => true,
        ]);

        $request = new Request('POST', '/v1/chat/completions', $headers, $body);

        try {
            $response = $client->send($request, ['stream' => true]);
            $stream = $response->getBody();

            while (!$stream->eof()) {
                $line = $stream->read(1024);
                echo $line;
                ob_flush();
                flush();
            }
        } catch (Exception $e) {
            echo "data: {\"error\": \"" . addslashes($e->getMessage()) . "\"}\n\n";
            ob_flush();
            flush();
        }
    };
}

function get_available_providers(): array
{
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
    $dotenv->load();

    $providers = [];

    // Check each provider and only include if API key is configured
    // For local providers (Ollama, LM Studio), we check if the key is explicitly set
    // For cloud providers, we check if the key is not empty

    if (isset($_ENV['OLLAMA_API_KEY'])) {
        $providers[] = ['id' => 'OLLAMA', 'name' => 'Ollama'];
    }

    if (isset($_ENV['LMSTUDIO_API_KEY'])) {
        $providers[] = ['id' => 'LM_STUDIO', 'name' => 'LM Studio'];
    }

    if (!empty($_ENV['MISTRAL_API_KEY'] ?? '')) {
        $providers[] = ['id' => 'MISTRAL', 'name' => 'Mistral'];
    }

    if (!empty($_ENV['DEEPSEEK_API_KEY'] ?? '')) {
        $providers[] = ['id' => 'DEEPSEEK', 'name' => 'DeepSeek'];
    }

    if (!empty($_ENV['OPENAI_COMPATIBLE_API_KEY'] ?? '')) {
        $providers[] = ['id' => 'OPENAI_COMPATIBLE', 'name' => 'OpenAI Compatible'];
    }

    // Always include at least one provider as fallback
    if (empty($providers)) {
        $providers[] = ['id' => 'OPENAI_COMPATIBLE', 'name' => 'OpenAI Compatible'];
    }

    return $providers;
}

function get_available_models(string $provider): array
{
    $config = get_provider_config($provider);
    $client = new Client(['base_uri' => $config['base_uri']]);

    $headers = [
        'Authorization' => 'Bearer ' . $config['api_key'],
    ];

    try {
        $response = $client->get('/v1/models', ['headers' => $headers]);
        $data = json_decode((string)$response->getBody(), true);
        return $data['data'] ?? [];
    } catch (Exception $e) {
        return [];
    }
}
