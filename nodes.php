<?php
declare(strict_types=1);

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/utils/llm_api.php';

use PocketFlow\AsyncNode;
use React\Promise\PromiseInterface;
use function React\Async\async;
use Symfony\Component\Yaml\Yaml;

class PlannerNode extends AsyncNode
{
    public function prep_async(stdClass $shared): PromiseInterface
    {
        return async(fn() => $shared->prompt)();
    }

    public function exec_async(mixed $prompt): PromiseInterface
    {
        $system_prompt = <<<PROMPT
You are an AI planning agent. Your task is to create a highly detailed and structured design plan based on the user's prompt. The final output will be a single HTML file.

First, think step-by-step about the user's request, outlining your reasoning. Then, generate the complete plan in valid YAML format.

You are a YAML generation agent. Your ONLY output format is a single, valid YAML code block. Do NOT add any text before or after the YAML block.

Your task is to generate a detailed design plan based on the user's prompt.

CRITICAL YAML SYNTAX RULES:
1. The entire response MUST be a single YAML block starting with `thinking:`.
2. All string values MUST be enclosed in double quotes (") and properly escaped.
3. The keys (`thinking`, `action`, `plan`, and all sub-keys) MUST be present exactly as specified.

Your response must follow this structure:

```yaml
thinking: |
  Your detailed, step-by-step reasoning about the user's prompt, the design choices, and the technologies to be used. This part will be streamed to the user first.
action: "generate_plan"
plan:
  project_overview_and_goals:
    purpose: "Summarize the website’s purpose (e.g., e-commerce, portfolio)."
    target_audience: "Identify the target audience."
    objectives: "Define specific objectives (e.g., drive conversions, enhance user engagement)."
  color_palette:
    primary: ["#HEXCODE1", "#HEXCODE2"]
    accent: ["#HEXCODE3"]
    notes: "Ensure WCAG 2.1 contrast ratios."
  typography:
    primary_font: "'Font Name', sans-serif"
    secondary_font: "'Font Name', serif"
    body_text:
      size: "16px"
      weight: "400"
      line_height: "1.6"
  layout_and_structure:
    desktop_wireframe: "Describe the layout for desktop (e.g., header with nav, hero section, 3-column feature grid, footer)."
    mobile_wireframe: "Describe the responsive layout for mobile."
    grid_system: "Specify CSS Grid or Flexbox usage."
  styling_and_visual_design:
    ui_elements: "Describe styles for buttons, cards, forms, hover effects."
    imagery_style: "Describe the style of images or illustrations."
    spacing: "Define consistent spacing, padding, and margins."
  technologies_and_frameworks:
    technologies: ["HTML5", "CSS3", "JavaScript ES6+"]
    frameworks: ["Tailwind CSS via CDN", "React via CDN with Babel for JSX"]
    justification: "Justify the technology choices."
  user_experience:
    user_flows: "Map out key user navigation paths."
    ctas: "Specify placement and design of Calls-to-Action."
    accessibility: ["ARIA labels", "Keyboard navigation", "Screen reader compatibility"]
  content_strategy:
    placeholder_types: "Describe placeholder content needed (e.g., text, images)."
    tone_and_voice: "Define the tone for copywriting (e.g., professional, friendly)."
    seo: ["Meta tags", "Alt text for images", "Keyword-friendly headings"]
  functionality_and_interactivity:
    interactive_elements: "Detail forms, modals, sliders, etc."
    third_party_integrations: "List any integrations like Google Analytics via CDN."
  performance_optimization:
    strategies: ["Optimized images", "Lazy loading", "Minified CSS/JS"]
    compatibility: ["Chrome", "Firefox", "Safari", "Edge"]
  security_considerations:
    protocols: "HTTPS for all CDN resources."
    best_practices: "Sanitizing inputs for forms."
  testing_plan:
    methods: ["Responsiveness testing", "Functionality testing", "Accessibility testing"]
    tools: ["Lighthouse", "BrowserStack"]
  branding_alignment:
    guidelines: "Adhere to brand identity or propose a cohesive theme."
  file_structure_and_delivery:
    confirmation: "All code will be in a single, self-contained HTML file."
```

User Prompt: "{$prompt}"
PROMPT;

        $messages = [
            ['role' => 'system', 'content' => $system_prompt],
            ['role' => 'user', 'content' => $prompt],
        ];

        $provider = $this->params['provider'];
        $model = $this->params['model'];

        return async(function () use ($provider, $model, $messages) {
            $stream = call_llm_stream($provider, $model, $messages);
            $stream();
        })();
    }

    public function post_async(stdClass $shared, mixed $p, mixed $plan): PromiseInterface
    {
        return async(function () use ($shared, $plan) {
            $shared->plan = $plan;
            return null;
        })();
    }
}

class EditPlannerNode extends AsyncNode
{
    public function prep_async(stdClass $shared): PromiseInterface
    {
        return async(fn() => [$shared->plan, $shared->edit_prompt])();
    }

    public function exec_async(mixed $data): PromiseInterface
    {
        [$plan, $edit_prompt] = $data;

        $planYaml = Yaml::dump($plan);

        $system_prompt = <<<PROMPT
You are an AI planning agent. Your task is to edit a detailed design plan based on the user's instructions.

First, think step-by-step about the user's request, outlining your reasoning for the changes. Then, generate the complete, updated plan in valid YAML format.

You are a YAML generation agent. Your ONLY output format is a single, valid YAML code block. Do NOT add any text before or after the YAML block.

Your task is to generate a detailed design plan based on the user's prompt.

CRITICAL YAML SYNTAX RULES:
1. The entire response MUST be a single YAML block starting with `thinking:`.
2. All string values MUST be enclosed in double quotes (") and properly escaped.
3. The keys (`thinking`, `action`, `plan`, and all sub-keys) MUST be present exactly as specified.

Your response must follow this structure:

```yaml
thinking: |
  Your detailed, step-by-step reasoning about the user's requested changes and how you will modify the plan.
action: "generate_plan"
plan:
  # ... (The full, updated YAML plan goes here, following the same structure as the original plan) ...
```

This is the current plan:
```yaml
{$planYaml}
```

This is the user's request to edit the plan:
"{$edit_prompt}"
PROMPT;

        $messages = [
            ['role' => 'system', 'content' => $system_prompt],
        ];

        $provider = $this->params['provider'];
        $model = $this->params['model'];

        return async(function () use ($provider, $model, $messages) {
            $stream = call_llm_stream($provider, $model, $messages);
            $stream();
        })();
    }

    public function post_async(stdClass $shared, mixed $p, mixed $plan): PromiseInterface
    {
        return async(function () use ($shared, $plan) {
            $shared->plan = $plan;
            return null;
        })();
    }
}

class GeneratorNode extends AsyncNode
{
    public function prep_async(stdClass $shared): PromiseInterface
    {
        return async(fn() => $shared->plan)();
    }

    public function exec_async(mixed $plan): PromiseInterface
    {
        $planYaml = Yaml::dump($plan);
        $system_prompt = <<<PROMPT
You are an HTML generation agent. Your ONLY output format is a single, valid HTML code block. Adhere STRICTLY to the following rules:

1.  Your entire response MUST be only the HTML code. Do NOT add any text, explanation, or markdown fences (e.g., ```html) before or after the code.
2.  You MUST follow the provided plan exactly. Do NOT add any elements, styles, or functionalities not specified in the plan.
3.  You MUST inject the following comments to indicate your progress:
    - `<!-- STEP: Generating HTML Structure -->`
    - `<!-- STEP: Adding Content -->`
    - `<!-- STEP: Adding Styles -->`
    - `<!-- STEP: Adding JavaScript -->`
    - `<!-- STEP: Finalizing -->`

Start your response IMMEDIATELY with the `<!DOCTYPE html>` declaration.

This is the plan you must follow:
```yaml
{$planYaml}
```
PROMPT;

        $messages = [
            ['role' => 'system', 'content' => $system_prompt],
        ];

        $provider = $this->params['provider'];
        $model = $this->params['model'];

        return async(function () use ($provider, $model, $messages) {
            $stream = call_llm_stream($provider, $model, $messages);
            $stream();
        })();
    }

    public function post_async(stdClass $shared, mixed $p, mixed $code): PromiseInterface
    {
        return async(function () use ($shared, $code) {
            $shared->code = $code;
            return null;
        })();
    }
}

class DiffNode extends AsyncNode
{
    public function prep_async(stdClass $shared): PromiseInterface
    {
        return async(fn() => [$shared->code, $shared->edit_prompt])();
    }

    public function exec_async(mixed $data): PromiseInterface
    {
        [$code, $edit_prompt] = $data;

        $lines = explode("\n", $code);
        $code_with_lines = "";
        foreach ($lines as $i => $line) {
            $code_with_lines .= ($i + 1) . ": " . $line . "\n";
        }

        $system_prompt = <<<PROMPT
You are a code editing agent. Your ONLY output format is a single, valid YAML code block.

Your task is to analyze the user's request and the provided HTML code. You must generate a single `replace_lines` action to be executed to fulfill the request.

CRITICAL YAML SYNTAX RULES:
1. The entire response MUST be a single YAML block starting with `action:`.
2. All string values MUST be enclosed in double quotes (") and properly escaped.
3. Line numbers are 1-based.

Your response must follow this structure:

```yaml
action:
  type: "replace_lines"
  start_line_number: <line number to start replacing from>
  end_line_number: <line number to end replacing at>
  new_code: |
    The new, multi-line block of HTML/CSS code that will replace the specified lines.
```

This is the current code (with line numbers for reference):
```
{$code_with_lines}
```

This is the user's request:
"{$edit_prompt}"
PROMPT;

        $messages = [
            ['role' => 'system', 'content' => $system_prompt],
        ];

        $provider = $this->params['provider'];
        $model = $this->params['model'];

        return async(function () use ($provider, $model, $messages) {
            $stream = call_llm_stream($provider, $model, $messages);
            $stream();
        })();
    }

    public function post_async(stdClass $shared, mixed $p, mixed $diff): PromiseInterface
    {
        return async(function () use ($shared, $diff) {
            $shared->diff = $diff;
            return null;
        })();
    }
}

class SaveProjectNode extends AsyncNode
{
    public function prep_async(stdClass $shared): PromiseInterface
    {
        return async(fn() => [$shared->plan, $shared->code, $shared->project_name])();
    }

    public function exec_async(mixed $data): PromiseInterface
    {
        [$plan, $code, $project_name] = $data;

        return async(function () use ($plan, $code, $project_name) {
            $databaseDirectory = __DIR__ . '/database';
            if (!is_dir($databaseDirectory)) {
                mkdir($databaseDirectory, 0777, true);
            }
            $projects = new \SleekDB\Store("projects", $databaseDirectory, ['auto_cache' => false]);
            $project = [
                'name' => $project_name,
                'plan' => $plan,
                'code' => $code,
                'created_at' => date('Y-m-d H:i:s'),
            ];
            $projects->insert($project);
        })();
    }

    public function post_async(stdClass $shared, mixed $p, mixed $result): PromiseInterface
    {
        return async(fn() => null)();
    }
}