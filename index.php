<?php
declare(strict_types=1);

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/utils/llm_api.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

if (isset($_GET['action']) && $_GET['action'] === 'get_providers') {
    header('Content-Type: application/json');
    echo json_encode(get_available_providers());
    exit;
}

if (isset($_GET['action']) && $_GET['action'] === 'get_models' && isset($_GET['provider'])) {
    header('Content-Type: application/json');
    echo json_encode(get_available_models($_GET['provider']));
    exit;
}

if (isset($_GET['action']) && $_GET['action'] === 'get_projects') {
    header('Content-Type: application/json');
    $databaseDirectory = __DIR__ . '/database';
    if (!is_dir($databaseDirectory)) {
        mkdir($databaseDirectory, 0777, true);
    }
    $projects = new \SleekDB\Store("projects", $databaseDirectory, ['auto_cache' => false]);
    $allProjects = $projects->findAll(["_id" => "desc"]);
    echo json_encode($allProjects);
    exit;
}

if (isset($_GET['action']) && $_GET['action'] === 'delete_project' && isset($_GET['id'])) {
    header('Content-Type: application/json');
    $databaseDirectory = __DIR__ . '/database';
    $projects = new \SleekDB\Store("projects", $databaseDirectory, ['auto_cache' => false]);
    $result = $projects->deleteById((int)$_GET['id']);
    echo json_encode(['success' => $result]);
    exit;
}

if (isset($_GET['action']) && in_array($_GET['action'], ['generate_plan', 'edit_plan', 'generate_code', 'diff_code', 'save_project'])) {
    header('Content-Type: text/event-stream');
    header('Cache-Control: no-cache');

    $_POST['provider'] = $_GET['provider'] ?? $_ENV['DEFAULT_PROVIDER'] ?? 'OPENAI_COMPATIBLE';
    $_POST['model'] = $_GET['model'] ?? 'gpt-3.5-turbo';
    $_POST['prompt'] = $_GET['prompt'] ?? '';
    $_POST['plan'] = $_GET['plan'] ?? '';
    $_POST['code'] = $_GET['code'] ?? '';
    $_POST['project_name'] = $_GET['project_name'] ?? '';
    $_POST['action'] = $_GET['action'];

    require 'main.php';

    exit;
}

?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Web Page Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.2/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js"></script>
    <!-- Space Mono Font for v1-ui styling -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    <style>
        :root {
            --background: oklch(0.99 0 0);
            --foreground: oklch(0 0 0);
            --card: oklch(1 0 0);
            --card-foreground: oklch(0 0 0);
            --popover: oklch(0.99 0 0);
            --popover-foreground: oklch(0 0 0);
            --primary: oklch(0 0 0);
            --primary-foreground: oklch(1 0 0);
            --secondary: oklch(0.94 0 0);
            --secondary-foreground: oklch(0 0 0);
            --muted: oklch(0.97 0 0);
            --muted-foreground: oklch(0.44 0 0);
            --accent: oklch(0.94 0 0);
            --accent-foreground: oklch(0 0 0);
            --destructive: oklch(0.63 0.19 23.03);
            --destructive-foreground: oklch(1 0 0);
            --border: oklch(0.92 0 0);
            --input: oklch(0.94 0 0);
            --ring: oklch(0 0 0);
            --radius: 0.5rem;
        }

        .dark {
            --background: oklch(0 0 0);
            --foreground: oklch(1 0 0);
            --card: oklch(0.14 0 0);
            --card-foreground: oklch(1 0 0);
            --popover: oklch(0.18 0 0);
            --popover-foreground: oklch(1 0 0);
            --primary: oklch(1 0 0);
            --primary-foreground: oklch(0 0 0);
            --secondary: oklch(0.25 0 0);
            --secondary-foreground: oklch(1 0 0);
            --muted: oklch(0.23 0 0);
            --muted-foreground: oklch(0.72 0 0);
            --accent: oklch(0.32 0 0);
            --accent-foreground: oklch(1 0 0);
            --destructive: oklch(0.69 0.2 23.91);
            --destructive-foreground: oklch(0 0 0);
            --border: oklch(0.26 0 0);
            --input: oklch(0.32 0 0);
            --ring: oklch(0.72 0 0);
        }

        body {
            background-color: #0a0a0f !important;
            color: #ffffff !important;
            font-family: 'Space Mono', monospace !important;
        }

        /* v1-ui Typing Animation */
        .typing-container {
            display: inline-block;
            position: relative;
        }

        .typing-text {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            width: 0;
            animation: typing 1.5s steps(20, end) forwards;
        }

        .typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: #ffffff;
            margin-left: 2px;
            animation: blink-caret 0.75s step-end infinite;
        }

        .typing-cursor.finished {
            display: none;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 21ch; } /* Full width of "WHAT ARE WE BUILDING?" */
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }

        /* v1-ui Dark Night Blue Theme Overrides */
        .container {
            background-color: #0a0a0f !important;
        }

        .card {
            background-color: #1a1a2e !important;
            border: 1px solid #16213e !important;
        }

        .textarea, .select, .input {
            background-color: #1a1a2e !important;
            border-color: #16213e !important;
            color: #ffffff !important;
        }

        .textarea::placeholder, .input::placeholder {
            color: #8892b0 !important;
        }

        .btn {
            font-family: 'Space Mono', monospace !important;
            font-weight: 600 !important;
            letter-spacing: 0.05em !important;
        }

        .modal-box {
            background-color: #1a1a2e !important;
            border: 1px solid #16213e !important;
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto p-4">
        <div class="text-center my-10">
            <h1 class="text-5xl font-bold mb-6" style="font-family: 'Space Mono', monospace; color: #ffffff;">
                <div class="typing-container">
                    <span id="typing-text" class="typing-text">WHAT ARE WE BUILDING?</span>
                    <span id="typing-cursor" class="typing-cursor"></span>
                </div>
            </h1>
            <div>
                <button id="view-projects-btn" class="btn bg-gray-800 text-white hover:bg-gray-700 border-gray-600">View Projects</button>
            </div>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="form-control">
                <textarea id="prompt" class="textarea h-24" style="background-color: #1a1a2e; border-color: #16213e; color: #ffffff;" placeholder="e.g., Generate a modern, responsive dark mode pricing page using Tailwind CSS"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 my-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text" style="color: #8892b0;">Provider</span>
                    </label>
                    <select id="provider" class="select" style="background-color: #1a1a2e; border-color: #16213e; color: #ffffff;">
                        <!-- Providers will be populated by JavaScript -->
                    </select>
                </div>

                <div class="form-control">
                    <label class="label">
                        <span class="label-text" style="color: #8892b0;">Model</span>
                    </label>
                    <select id="model" class="select" style="background-color: #1a1a2e; border-color: #16213e; color: #ffffff;">
                        <!-- Models will be populated by JavaScript -->
                    </select>
                </div>

                <div class="form-control">
                    <label class="label">
                        <span class="label-text">&nbsp;</span>
                    </label>
                    <button id="generate-btn" class="btn bg-white text-black hover:bg-gray-200 font-bold tracking-wider">GENERATE</button>
                </div>
            </div>
        </div>

        <div id="plan-container" class="max-w-4xl mx-auto my-10 hidden">
            <div class="card shadow-xl" style="background-color: #1a1a2e; border: 1px solid #16213e;">
                <div class="card-body">
                    <h2 class="card-title text-white">Design Plan</h2>
                    <div id="plan-output" class="text-white"></div>
                    <div id="plan-actions" class="card-actions justify-end hidden">
                        <button id="edit-plan-btn" class="btn font-bold tracking-wider" style="background-color: #16213e; color: #ffffff;">EDIT PLAN</button>
                        <button id="approve-plan-btn" class="btn bg-white text-black hover:bg-gray-200 font-bold tracking-wider">APPROVE</button>
                    </div>
                </div>
            </div>
        </div>

        <dialog id="edit-plan-modal" class="modal">
            <div class="modal-box" style="background-color: #1a1a2e; border: 1px solid #16213e;">
                <h3 class="font-bold text-lg text-white">Edit Plan</h3>
                <div class="modal-action">
                    <form method="dialog" class="w-full">
                        <textarea id="edit-prompt" class="textarea h-24 w-full text-white" style="background-color: #16213e; border-color: #0f172a; color: #ffffff;" placeholder="e.g., Change the color palette to a more vibrant one."></textarea>
                        <button id="submit-edit-btn" class="btn mt-4 bg-white text-black hover:bg-gray-200 font-bold tracking-wider">SUBMIT</button>
                    </form>
                </div>
            </div>
        </dialog>
    </div>

    <div id="generation-view" class="hidden" style="background-color: #0a0a0f;">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 h-screen">
            <div class="h-full">
                <div id="editor" class="h-3/4"></div>
                <div id="work-steps" class="h-1/4 p-4 text-white" style="background-color: #1a1a2e; border-top: 1px solid #16213e;"></div>
            </div>
            <div class="h-full">
                <iframe id="preview" class="w-full h-full border-0"></iframe>
            </div>
        </div>
        <button id="edit-code-btn" class="btn fixed bottom-4 right-4 font-bold tracking-wider" style="background-color: #16213e; color: #ffffff;">EDIT CODE</button>
        <button id="save-project-btn" class="btn fixed bottom-4 right-28 bg-white text-black hover:bg-gray-200 font-bold tracking-wider">SAVE PROJECT</button>
    </div>

    <dialog id="edit-code-modal" class="modal">
        <div class="modal-box" style="background-color: #1a1a2e; border: 1px solid #16213e;">
            <h3 class="font-bold text-lg text-white">Edit Code</h3>
            <div class="modal-action">
                <form method="dialog" class="w-full">
                    <textarea id="edit-code-prompt" class="textarea h-24 w-full text-white" style="background-color: #16213e; border-color: #0f172a; color: #ffffff;" placeholder="e.g., Make the buttons green."></textarea>
                    <button id="submit-code-edit-btn" class="btn mt-4 bg-white text-black hover:bg-gray-200 font-bold tracking-wider">SUBMIT</button>
                </form>
            </div>
        </div>
    </dialog>

    <dialog id="save-project-modal" class="modal">
        <div class="modal-box" style="background-color: #1a1a2e; border: 1px solid #16213e;">
            <h3 class="font-bold text-lg text-white">Save Project</h3>
            <div class="modal-action">
                <form method="dialog" class="w-full">
                    <input type="text" id="project-name" class="input w-full text-white" style="background-color: #16213e; border-color: #0f172a; color: #ffffff;" placeholder="Enter project name" />
                    <button id="submit-save-btn" class="btn mt-4 bg-white text-black hover:bg-gray-200 font-bold tracking-wider">SAVE</button>
                </form>
            </div>
        </div>
    </dialog>

    <dialog id="view-projects-modal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl" style="background-color: #1a1a2e; border: 1px solid #16213e;">
            <h3 class="font-bold text-lg text-white">Saved Projects</h3>
            <div id="projects-list" class="my-4 max-h-[60vh] overflow-y-auto text-white">
                <!-- Projects will be populated here -->
            </div>
            <div class="modal-action">
                <form method="dialog">
                    <button class="btn font-bold tracking-wider" style="background-color: #16213e; color: #ffffff;">CLOSE</button>
                </form>
            </div>
        </div>
    </dialog>

    <dialog id="delete-confirm-modal" class="modal">
        <div class="modal-box" style="background-color: #1a1a2e; border: 1px solid #16213e;">
            <h3 class="font-bold text-lg text-white">Are you sure?</h3>
            <p class="py-4 text-white">Do you really want to delete this project? This action cannot be undone.</p>
            <div class="modal-action">
                <button id="cancel-delete-btn" class="btn font-bold tracking-wider" style="background-color: #16213e; color: #ffffff;">CANCEL</button>
                <button id="confirm-delete-btn" class="btn bg-red-600 text-white hover:bg-red-700 font-bold tracking-wider">DELETE</button>
            </div>
        </div>
    </dialog>

    <script>
        // Pass PHP environment variables to JavaScript
        const DEFAULT_PROVIDER = '<?php echo $_ENV['DEFAULT_PROVIDER'] ?? 'OPENAI_COMPATIBLE'; ?>';
        console.log('DEFAULT_PROVIDER from PHP:', DEFAULT_PROVIDER);

        document.addEventListener('DOMContentLoaded', () => {
            // Fix typing animation - remove blinking cursor after completion
            const typingCursor = document.getElementById('typing-cursor');
            if (typingCursor) {
                setTimeout(() => {
                    typingCursor.classList.add('finished');
                }, 1600); // Slightly after the typing animation completes
            }

            // --- DOM Elements ---
            const providerSelect = document.getElementById('provider');
            const modelSelect = document.getElementById('model');
            const generateBtn = document.getElementById('generate-btn');
            const promptTextarea = document.getElementById('prompt');
            const planContainer = document.getElementById('plan-container');
            const planOutput = document.getElementById('plan-output');
            const planActions = document.getElementById('plan-actions');
            const editPlanBtn = document.getElementById('edit-plan-btn');
            const approvePlanBtn = document.getElementById('approve-plan-btn');
            const editPlanModal = document.getElementById('edit-plan-modal');
            const editPromptTextarea = document.getElementById('edit-prompt');
            const submitEditBtn = document.getElementById('submit-edit-btn');
            const generationView = document.getElementById('generation-view');
            const editCodeBtn = document.getElementById('edit-code-btn');
            const saveProjectBtn = document.getElementById('save-project-btn');
            const editCodeModal = document.getElementById('edit-code-modal');
            const editCodePromptTextarea = document.getElementById('edit-code-prompt');
            const submitCodeEditBtn = document.getElementById('submit-code-edit-btn');
            const viewProjectsBtn = document.getElementById('view-projects-btn');
            const viewProjectsModal = document.getElementById('view-projects-modal');
            const projectsList = document.getElementById('projects-list');
            const saveProjectModal = document.getElementById('save-project-modal');
            const projectNameInput = document.getElementById('project-name');
            const submitSaveBtn = document.getElementById('submit-save-btn');
            const deleteConfirmModal = document.getElementById('delete-confirm-modal');
            const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
            const cancelDeleteBtn = document.getElementById('cancel-delete-btn');

            // --- State ---
            let currentPlan = null;
            let editor = null;
            let projectToDeleteId = null;

            // --- UI Elements ---
            const thinkingOutput = document.createElement('pre');
            thinkingOutput.className = 'whitespace-pre-wrap';
            const loadingSpinner = document.createElement('div');
            loadingSpinner.className = 'loading loading-spinner loading-lg';

            // --- Functions ---

            const fetchProviders = async () => {
                try {
                    const response = await fetch(`?action=get_providers`);
                    const providers = await response.json();
                    providerSelect.innerHTML = '';
                    providers.forEach(provider => {
                        const option = document.createElement('option');
                        option.value = provider.id;
                        option.textContent = provider.name;
                        providerSelect.appendChild(option);
                    });

                    // Set default provider after populating options
                    console.log('Available providers:', providers);
                    console.log('Looking for DEFAULT_PROVIDER:', DEFAULT_PROVIDER);

                    if (providers.some(p => p.id === DEFAULT_PROVIDER)) {
                        console.log('Found DEFAULT_PROVIDER, setting to:', DEFAULT_PROVIDER);
                        providerSelect.value = DEFAULT_PROVIDER;
                    } else if (providers.length > 0) {
                        console.log('DEFAULT_PROVIDER not found, falling back to first provider:', providers[0].id);
                        providerSelect.value = providers[0].id;
                    }

                    console.log('Final selected provider:', providerSelect.value);

                    // Fetch models for the selected provider
                    await fetchModels();
                } catch (error) {
                    console.error('Error fetching providers:', error);
                }
            };

            const fetchModels = async () => {
                const provider = providerSelect.value;
                try {
                    const response = await fetch(`?action=get_models&provider=${provider}`);
                    const models = await response.json();
                    modelSelect.innerHTML = '';
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = model.id; // Use ID as name
                        modelSelect.appendChild(option);
                    });
                } catch (error) {
                    console.error('Error fetching models:', error);
                }
            };

            const fetchProjects = async () => {
                try {
                    const response = await fetch(`?action=get_projects`);
                    const projects = await response.json();
                    displayProjects(projects);
                } catch (error) {
                    console.error('Error fetching projects:', error);
                    projectsList.innerHTML = '<p class="text-error">Could not load projects.</p>';
                }
            };

            const displayProjects = (projects) => {
                projectsList.innerHTML = '';
                if (projects.length === 0) {
                    projectsList.innerHTML = '<p>No saved projects found.</p>';
                    return;
                }

                const table = document.createElement('table');
                table.className = 'table w-full';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Purpose</th>
                            <th>Created</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                `;
                const tbody = table.querySelector('tbody');

                projects.forEach(project => {
                    const tr = document.createElement('tr');
                    const name = project.name || 'Untitled';
                    const purpose = project.plan?.project_overview_and_goals?.purpose || 'N/A';
                    const createdAt = new Date(project.created_at).toLocaleString();
                    tr.innerHTML = `
                        <td>${name}</td>
                        <td>${purpose}</td>
                        <td>${createdAt}</td>
                        <td class="text-right space-x-2">
                            <button class="btn btn-sm load-project-btn" style="background-color: var(--primary); color: var(--primary-foreground);">Load</button>
                            <button class="btn btn-error btn-sm delete-project-btn" data-project-id="${project._id}">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(tr);

                    tr.querySelector('.load-project-btn').addEventListener('click', () => {
                        loadProject(project);
                    });

                    tr.querySelector('.delete-project-btn').addEventListener('click', (e) => {
                        projectToDeleteId = e.currentTarget.dataset.projectId;
                        deleteConfirmModal.showModal();
                    });
                });

                projectsList.appendChild(table);
            };

            const loadProject = (project) => {
                currentPlan = project.plan;
                
                document.querySelector('.container').classList.add('hidden');
                generationView.classList.remove('hidden');

                require.config({ paths: { 'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs' } });
                require(['vs/editor/editor.main'], () => {
                    if (editor) {
                        editor.dispose();
                    }
                    editor = monaco.editor.create(document.getElementById('editor'), {
                        value: project.code,
                        language: 'html',
                        theme: 'vs-dark'
                    });

                    const updatePreview = () => {
                        const code = editor.getValue();
                        document.getElementById('preview').srcdoc = code;
                    };
                    
                    editor.onDidChangeModelContent(updatePreview);
                    updatePreview();
                });

                viewProjectsModal.close();
            };

            const cleanYamlString = (rawString) => {
                const match = rawString.match(/(thinking:|action:|actions:)[\s\S]*/);
                if (match) {
                    let cleanString = match[0];
                    cleanString = cleanString.replace(/```yaml/g, '').replace(/```/g, '');
                    return cleanString.trim();
                }
                return rawString; // Fallback if no match is found
            };

            const handleStream = (url, handlers) => {
                const eventSource = new EventSource(url);
                let buffer = '';

                eventSource.onmessage = (event) => {
                    if (event.data === '[DONE]') {
                        eventSource.close();
                        if (handlers.onDone) handlers.onDone(buffer);
                        return;
                    }

                    try {
                        const data = JSON.parse(event.data);
                        if (data.choices && data.choices[0].delta.content) {
                            buffer += data.choices[0].delta.content;
                            if (handlers.onData) handlers.onData(buffer);
                        }
                    } catch (e) {
                        // Ignore parsing errors from incomplete JSON
                    }
                };

                eventSource.onerror = (err) => {
                    console.error('EventSource failed:', err);
                    eventSource.close();
                    if (handlers.onError) handlers.onError();
                };
            };

            const handlePlanGeneration = (prompt, action) => {
                planContainer.classList.remove('hidden');
                planOutput.innerHTML = '';
                thinkingOutput.textContent = '';
                planOutput.appendChild(thinkingOutput);
                planActions.classList.add('hidden');

                const url = `?action=${action}&provider=${providerSelect.value}&model=${modelSelect.value}&prompt=${encodeURIComponent(prompt)}&plan=${encodeURIComponent(JSON.stringify(currentPlan))}`;
                let thinkingRendered = false;

                handleStream(url, {
                    onData: (buffer) => {
                        const thinkingMatch = buffer.match(/thinking:\s*\|?\s*([\s\S]*?)(?=action:|$)/);
                        if (thinkingMatch && thinkingMatch[1]) {
                            thinkingOutput.textContent = thinkingMatch[1].trim();
                        }

                        if (!thinkingRendered && buffer.includes('action: "generate_plan"')) {
                            planOutput.appendChild(loadingSpinner);
                            thinkingRendered = true;
                        }
                    },
                    onDone: (buffer) => {
                        try {
                            const cleanBuffer = cleanYamlString(buffer);
                            const yaml = jsyaml.load(cleanBuffer);
                            if (yaml && yaml.plan) {
                                planOutput.innerHTML = ''; // Clear thinking and spinner
                                currentPlan = yaml.plan;
                                displayPlan(currentPlan);
                                planActions.classList.remove('hidden');
                            } else {
                                planOutput.innerHTML = '<p class="text-error">Failed to parse the plan from the response.</p>';
                            }
                        } catch (e) {
                            console.error('YAML parsing error:', e);
                            planOutput.innerHTML = `<p class="text-error">Error parsing final plan. Please check the console.</p><pre>${buffer}</pre>`;
                        }
                    },
                    onError: () => {
                        planOutput.innerHTML = '<p class="text-error">An error occurred during plan generation.</p>';
                    }
                });
            };

            const handleCodeGeneration = () => {
                document.querySelector('.container').classList.add('hidden');
                generationView.classList.remove('hidden');

                require.config({ paths: { 'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs' } });
                require(['vs/editor/editor.main'], () => {
                    if (editor) {
                        editor.dispose();
                    }
                    editor = monaco.editor.create(document.getElementById('editor'), {
                        value: '<!-- Generating code... -->',
                        language: 'html',
                        theme: 'vs-dark'
                    });

                    const updatePreview = () => {
                        const code = editor.getValue();
                        document.getElementById('preview').srcdoc = code;
                    };

                    editor.onDidChangeModelContent(updatePreview);

                    const workStepsContainer = document.getElementById('work-steps');
                    const workSteps = ['Initializing', 'Generating HTML', 'Adding Styles', 'Adding JavaScript', 'Finalizing'];
                    let currentStep = 0;

                    const updateWorkSteps = (step) => {
                        const stepIndex = workSteps.indexOf(step);
                        if (stepIndex > currentStep) currentStep = stepIndex;

                        workStepsContainer.innerHTML = '';
                        workSteps.forEach((s, i) => {
                            const stepEl = document.createElement('div');
                            stepEl.className = 'flex items-center gap-2 my-2';
                            const icon = document.createElement('div');
                            if (i < currentStep) {
                                icon.className = 'badge badge-success';
                                icon.textContent = '✓';
                            } else if (i === currentStep) {
                                icon.className = 'loading loading-spinner loading-xs';
                            } else {
                                icon.className = 'badge badge-neutral';
                            }
                            stepEl.appendChild(icon);
                            stepEl.appendChild(document.createTextNode(s));
                            workStepsContainer.appendChild(stepEl);
                        });
                    };
                    updateWorkSteps('Initializing');

                    const url = `?action=generate_code&provider=${providerSelect.value}&model=${modelSelect.value}&plan=${encodeURIComponent(JSON.stringify(currentPlan))}`;
                    handleStream(url, {
                        onData: (buffer) => {
                            editor.setValue(buffer);
                            const match = buffer.match(/<!-- STEP: (.*?) -->/g);
                            if (match) {
                                const lastStep = match[match.length - 1].replace('<!-- STEP: ', '').replace(' -->', '');
                                updateWorkSteps(lastStep);
                            }
                        },
                        onDone: () => {
                            currentStep = workSteps.length; // Mark all as complete
                            updateWorkSteps();
                        }
                    });
                });
            };
            
            const handleCodeEditing = (prompt) => {
                 submitCodeEditBtn.classList.add("loading");
                 const workStepsContainer = document.getElementById('work-steps');
                 workStepsContainer.innerHTML = '<div class="flex items-center gap-2 my-2"><div class="loading loading-spinner loading-xs"></div>Applying Edits...</div>';

                 const url = `?action=diff_code&provider=${providerSelect.value}&model=${modelSelect.value}&prompt=${encodeURIComponent(prompt)}&code=${encodeURIComponent(editor.getValue())}`;
                 handleStream(url, {
                    onDone: (buffer) => {
                        submitCodeEditBtn.classList.remove("loading");
                        workStepsContainer.innerHTML = '<div class="flex items-center gap-2 my-2"><div class="badge badge-success">✓</div>Finished</div>';
                        console.log("Raw response from agent:", buffer);
                        try {
                            const cleanBuffer = cleanYamlString(buffer);
                            console.log("Cleaned YAML:", cleanBuffer);
                            const yaml = jsyaml.load(cleanBuffer);
                            console.log("Parsed action:", yaml);

                            if (yaml && yaml.action && yaml.action.type === 'replace_lines') {
                                const action = yaml.action;
                                const model = editor.getModel();
                                const range = new monaco.Range(action.start_line_number, 1, action.end_line_number, model.getLineMaxColumn(action.end_line_number));
                                
                                try {
                                    editor.executeEdits("ai-agent", [{ range: range, text: action.new_code }]);
                                    // Flash effect
                                    const newRange = new monaco.Range(action.start_line_number, 1, action.start_line_number + action.new_code.split('\n').length - 1, model.getLineMaxColumn(action.start_line_number + action.new_code.split('\n').length - 1));
                                    const decoration = model.deltaDecorations([], [
                                        { range: newRange, options: { className: 'flash-decoration' } }
                                    ]);
                                    setTimeout(() => {
                                        model.deltaDecorations(decoration, []);
                                    }, 1500);
                                } catch (e) {
                                    console.error("Error applying edits to Monaco editor:", e);
                                }
                            } else {
                                console.error('Invalid response from diff agent:', yaml);
                            }
                        } catch (e) {
                            console.error('Error parsing diff response:', e);
                        }
                    },
                    onError: () => {
                        submitCodeEditBtn.classList.remove("loading");
                        workStepsContainer.innerHTML = '<div class="flex items-center gap-2 my-2"><div class="badge badge-error">✗</div>Error</div>';
                    }
                 });
            };

            const displayPlan = (plan) => {
                const planHtml = document.createElement('div');
                for (const [key, value] of Object.entries(plan)) {
                    const section = document.createElement('div');
                    section.className = 'my-4';
                    const title = document.createElement('h3');
                    title.className = 'text-xl font-bold';
                    title.textContent = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    section.appendChild(title);

                    if (key === 'color_palette' && typeof value === 'object') {
                        const colors = document.createElement('div');
                        colors.className = 'flex flex-wrap gap-4';
                        for (const [name, hex] of Object.entries(value)) {
                             if(typeof hex === 'string' && hex.startsWith('#')) {
                                const colorContainer = document.createElement('div');
                                const colorBox = document.createElement('div');
                                colorBox.className = 'w-16 h-16 rounded-lg shadow-lg border-2 border-base-300';
                                colorBox.style.backgroundColor = hex;
                                const colorName = document.createElement('p');
                                colorName.className = 'text-sm font-bold';
                                colorName.textContent = name;
                                const colorHex = document.createElement('p');
                                colorHex.className = 'text-xs';
                                colorHex.textContent = hex;
                                colorContainer.appendChild(colorBox);
                                colorContainer.appendChild(colorName);
                                colorContainer.appendChild(colorHex);
                                colors.appendChild(colorContainer);
                            } else if (Array.isArray(hex)) {
                                 hex.forEach(h => {
                                    const colorContainer = document.createElement('div');
                                    const colorBox = document.createElement('div');
                                    colorBox.className = 'w-16 h-16 rounded-lg shadow-lg border-2 border-base-300';
                                    colorBox.style.backgroundColor = h;
                                    const colorName = document.createElement('p');
                                    colorName.className = 'text-sm font-bold';
                                    colorName.textContent = name;
                                    const colorHex = document.createElement('p');
                                    colorHex.className = 'text-xs';
                                    colorHex.textContent = h;
                                    colorContainer.appendChild(colorBox);
                                    colorContainer.appendChild(colorName);
                                    colorContainer.appendChild(colorHex);
                                    colors.appendChild(colorContainer);
                                 });
                            }
                        }
                        section.appendChild(colors);
                    } else if (typeof value === 'object') {
                        const pre = document.createElement('pre');
                        pre.className = 'whitespace-pre-wrap bg-base-300 p-4 rounded-lg';
                        pre.textContent = jsyaml.dump(value, { indent: 2 });
                        section.appendChild(pre);
                    } else {
                        const p = document.createElement('p');
                        p.textContent = value;
                        section.appendChild(p);
                    }
                    planHtml.appendChild(section);
                }
                planOutput.appendChild(planHtml);
            };

            // --- Event Listeners ---
            generateBtn.addEventListener('click', () => handlePlanGeneration(promptTextarea.value, 'generate_plan'));
            editPlanBtn.addEventListener('click', () => editPlanModal.showModal());
            submitEditBtn.addEventListener('click', (e) => {
                e.preventDefault();
                handlePlanGeneration(editPromptTextarea.value, 'edit_plan');
                editPromptTextarea.value = '';
                editPlanModal.close();
            });
            approvePlanBtn.addEventListener('click', handleCodeGeneration);
            editCodeBtn.addEventListener('click', () => editCodeModal.showModal());
            submitCodeEditBtn.addEventListener('click', (e) => {
                e.preventDefault();
                handleCodeEditing(editCodePromptTextarea.value);
                editCodePromptTextarea.value = '';
                editCodeModal.close();
            });
            saveProjectBtn.addEventListener('click', () => {
                saveProjectModal.showModal();
            });
            submitSaveBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const projectName = projectNameInput.value;
                if (projectName) {
                    const url = `?action=save_project&project_name=${encodeURIComponent(projectName)}&plan=${encodeURIComponent(JSON.stringify(currentPlan))}&code=${encodeURIComponent(editor.getValue())}`;
                    fetch(url).then(() => {
                        alert('Project Saved!');
                        projectNameInput.value = '';
                        saveProjectModal.close();
                    });
                }
            });
            providerSelect.addEventListener('change', fetchModels);
            viewProjectsBtn.addEventListener('click', () => {
                fetchProjects();
                viewProjectsModal.showModal();
            });
            confirmDeleteBtn.addEventListener('click', async () => {
                if (projectToDeleteId) {
                    try {
                        const response = await fetch(`?action=delete_project&id=${projectToDeleteId}`);
                        const result = await response.json();
                        if (result.success) {
                            fetchProjects(); // Refresh the list
                        } else {
                            console.error('Failed to delete project.');
                        }
                    } catch (error) {
                        console.error('Error deleting project:', error);
                    }
                    projectToDeleteId = null;
                    deleteConfirmModal.close();
                }
            });
            cancelDeleteBtn.addEventListener('click', () => {
                projectToDeleteId = null;
                deleteConfirmModal.close();
            });

            // --- Initial Load ---
            fetchProviders();
        });
    </script>
</body>
</html>