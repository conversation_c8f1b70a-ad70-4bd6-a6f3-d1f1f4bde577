<?php
declare(strict_types=1);

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/flow.php';

use function React\Async\async;
use function React\Async\await;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

async(function () {
    $action = $_POST['action'] ?? 'generate_plan';

    $shared = new stdClass();
    $shared->action = $action;

    if ($action === 'edit_plan') {
        $shared->plan = json_decode($_POST['plan'] ?? '', true);
        $shared->edit_prompt = $_POST['prompt'] ?? '';
        $flow = create_edit_planning_flow();
    } elseif ($action === 'generate_code') {
        $shared->plan = json_decode($_POST['plan'] ?? '', true);
        $flow = create_generation_flow();
    } elseif ($action === 'diff_code') {
        $shared->code = $_POST['code'] ?? '';
        $shared->edit_prompt = $_POST['prompt'] ?? '';
        $flow = create_diff_flow();
    } elseif ($action === 'save_project') {
        $shared->plan = json_decode($_POST['plan'] ?? '', true);
        $shared->code = $_POST['code'] ?? '';
        $shared->project_name = $_POST['project_name'] ?? 'Untitled';
        $flow = create_save_project_flow();
    } else { // Default to generate_plan
        $shared->prompt = $_POST['prompt'] ?? '';
        $flow = create_planning_flow();
    }

    $flow->setParams([
        'provider' => $_POST['provider'] ?? $_ENV['DEFAULT_PROVIDER'] ?? 'OPENAI_COMPATIBLE',
        'model' => $_POST['model'] ?? 'gpt-3.5-turbo',
    ]);

    await($flow->run_async($shared));
})();
