# Default LLM Provider (options: <PERSON><PERSON>AM<PERSON>, <PERSON>M_STUDIO, MISTRA<PERSON>, <PERSON>EPSEEK, OPENAI_COMPATIBLE)
DEFAULT_PROVIDER=OPENAI_COMPATIBLE

# To disable a provider, comment out its API key line with #
# Only providers with configured API keys will appear in the dropdown

# Ollama Configuration (local)
OLLAMA_API_KEY=ollama

# LM Studio Configuration (local)
LMSTUDIO_API_KEY=lmstudio

# Mistral Configuration
# MISTRAL_API_KEY=your_mistral_api_key_here

# DeepSeek Configuration
# DEEPSEEK_API_KEY=your_deepseek_api_key_here

# OpenAI Compatible API Configuration
# This can be used for OpenAI, Azure OpenAI, or any OpenAI-compatible API
OPENAI_COMPATIBLE_API_KEY=your_openai_compatible_api_key_here
OPENAI_COMPATIBLE_BASE_URL=https://api.openai.com/v1

# Examples for different OpenAI-compatible providers:
# For OpenAI: https://api.openai.com/v1
# For Azure OpenAI: https://your-resource-name.openai.azure.com/openai/deployments/your-deployment-name
# For other providers: check their documentation for the correct base URL
