<?php
declare(strict_types=1);

require_once __DIR__ . '/nodes.php';

use PocketFlow\AsyncFlow;

function create_planning_flow(): AsyncFlow
{
    $plannerNode = new PlannerNode();
    return new AsyncFlow($plannerNode);
}

function create_edit_planning_flow(): AsyncFlow
{
    $editPlannerNode = new EditPlannerNode();
    return new AsyncFlow($editPlannerNode);
}

function create_generation_flow(): AsyncFlow
{
    $generatorNode = new GeneratorNode();
    return new AsyncFlow($generatorNode);
}

function create_diff_flow(): AsyncFlow
{
    $diffNode = new DiffNode();
    return new AsyncFlow($diffNode);
}

function create_save_project_flow(): AsyncFlow
{
    $saveProjectNode = new SaveProjectNode();
    return new AsyncFlow($saveProjectNode);
}
